{"name": "itels-reading", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:push": "prisma db push", "prisma:seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/static-data/seed-articles.ts", "prisma:seed:words": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/static-data/seed-words.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci", "seed:words": "ts-node --compiler-options {\\\"module\\\":\\\"CommonJS\\\"} prisma/static-data/seed-words.ts", "analyze": "cross-env ANALYZE=true next build"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@google/genai": "^1.8.0", "@hookform/resolvers": "^5.0.1", "@opennextjs/aws": "^3.6.2", "@opennextjs/cloudflare": "^1.0.4", "@prisma/adapter-neon": "^6.10.1", "@prisma/client": "^6.8.2", "@prisma/extension-accelerate": "^1.3.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tanstack/react-query": "^5.81.2", "@tanstack/react-table": "^8.21.3", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "better-auth": "^1.2.9-beta.10", "canvas": "^3.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "depcheck": "^1.4.7", "lucide-react": "^0.486.0", "next": "15.2.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-markdown": "^10.1.0", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "resend": "^4.5.2", "shadcn": "^2.4.0-canary.17", "sharp": "^0.34.2", "sonner": "^2.0.5", "tailwind": "^4.0.0", "tailwind-merge": "^3.1.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "vitest": "^3.2.4", "zod": "^3.25.71"}, "devDependencies": {"@babel/plugin-syntax-import-assertions": "^7.27.1", "@babel/plugin-syntax-import-attributes": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@biomejs/biome": "1.9.4", "@next/bundle-analyzer": "^14.2.3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20.19.1", "@types/react": "^19.1.8", "@types/react-dom": "^19", "autoprefixer": "^10.0.1", "babel-jest": "^30.0.0-beta.3", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "14.1.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "postcss": "^8", "prisma": "^6.8.2", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "wrangler": "^4.16.0"}}